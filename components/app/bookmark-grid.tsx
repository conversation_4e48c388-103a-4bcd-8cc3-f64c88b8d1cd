'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Grid, List, Loader2 } from 'lucide-react';
import { useBookmarks } from '@/hooks/use-bookmarks';
import { useLanguage } from '@/hooks/use-language';
import { BookmarkCard } from './bookmark-card';
import { BookmarkListItem } from './bookmark-list-item';
import type { Bookmark } from '@/types';

interface BookmarkGridProps {
  filters?: {
    is_archived?: boolean;
    tags?: string[];
    search?: string;
  };
  title?: string;
  showHeader?: boolean;
}

export function BookmarkGrid({ filters, title, showHeader = true }: BookmarkGridProps) {
  const { t } = useLanguage();
  const { bookmarks, isLoading, markAsRead, updateBookmark, deleteBookmark } =
    useBookmarks(filters);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const isSearching = filters?.search && filters.search.trim().length > 0;

  const handleMarkAsRead = async (bookmark: Bookmark) => {
    if (!bookmark.last_open) {
      try {
        await markAsRead(bookmark.id);
      } catch (error) {
        console.error('Error marking as read:', error);
      }
    }
  };

  const handleDelete = async (bookmark: Bookmark) => {
    try {
      await deleteBookmark(bookmark.id);
    } catch (error) {
      console.error('Error deleting bookmark:', error);
    }
  };

  const isUnread = (bookmark: Bookmark) => !bookmark.last_open;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (bookmarks.length === 0) {
    return (
      <div className="text-center py-12">
        {isSearching ? (
          <div className="space-y-3">
            <p className="text-gray-500 dark:text-gray-400 text-lg">
              "{filters?.search}" {t.noSearchResults}
            </p>
            <p className="text-gray-400 dark:text-gray-500 text-sm">
              {t.tryDifferentSearch}
            </p>
          </div>
        ) : (
          <p className="text-gray-500 dark:text-gray-400">{t.noBookmarks}</p>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      {showHeader && (
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {title || t.recentBookmarks}
          </h2>
          <div className="flex items-center space-x-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="flex items-center space-x-2"
            >
              <Grid className="w-4 h-4" />
              <span>{t.grid}</span>
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="flex items-center space-x-2"
            >
              <List className="w-4 h-4" />
              <span>{t.list}</span>
            </Button>
          </div>
        </div>
      )}

      {/* Bookmarks Content */}
      {viewMode === 'grid' ? (
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {bookmarks.map((bookmark) => (
            <BookmarkCard
              key={bookmark.id}
              bookmark={bookmark}
              onMarkAsRead={handleMarkAsRead}
              onDelete={handleDelete}
              isUnread={isUnread(bookmark)}
            />
          ))}
        </div>
      ) : (
        <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 dark:bg-gray-800/50">
                <TableHead className="w-12"></TableHead>
                <TableHead>{t.bookmarkTitle}</TableHead>
                <TableHead className="w-48">{t.tags}</TableHead>
                <TableHead className="w-32">{t.date}</TableHead>
                <TableHead className="w-24">{t.actions}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {bookmarks.map((bookmark) => (
                <BookmarkListItem
                  key={bookmark.id}
                  bookmark={bookmark}
                  onMarkAsRead={handleMarkAsRead}
                  onDelete={handleDelete}
                  isUnread={isUnread(bookmark)}
                />
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Load More */}
      {bookmarks.length > 0 && (
        <div className="text-center">
          <Button
            variant="outline"
            size="lg"
            className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
          >
            {t.loadMore}
          </Button>
        </div>
      )}
    </div>
  );
}

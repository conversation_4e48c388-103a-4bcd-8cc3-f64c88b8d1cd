'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from './use-auth';
import type { Bookmark, SearchFilters } from '@/types';

export function useBookmarks(filters?: SearchFilters) {
  const { user } = useAuth();
  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchBookmarks = async () => {
    if (!user) {
      setBookmarks([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      let query = supabase
        .from('bookmarks')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters?.is_archived !== undefined) {
        query = query.eq('is_archived', filters.is_archived);
      }

      if (filters?.tags && filters.tags.length > 0) {
        query = query.contains('tags', filters.tags);
      }

      if (filters?.before) {
        query = query.lt('created_at', filters.before);
      }

      if (filters?.after) {
        query = query.gt('created_at', filters.after);
      }

      // Apply search query
      if (filters?.search && filters.search.trim()) {
        const searchTerm = filters.search.trim();
        query = query.or(
          `title.ilike.%${searchTerm}%,url.ilike.%${searchTerm}%,summary.ilike.%${searchTerm}%`,
        );
      }

      const { data, error: fetchError } = await query;

      if (fetchError) throw fetchError;

      setBookmarks(data || []);
    } catch (err: any) {
      setError(err.message);
      console.error('Error fetching bookmarks:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBookmarks();
  }, [user, filters]);

  const addBookmark = async (url: string, title?: string, summary?: string, tags?: string[]) => {
    if (!user) throw new Error('User not authenticated');

    try {
      const { data, error } = await supabase
        .from('bookmarks')
        .insert({
          user_id: user.id,
          url,
          title,
          summary,
          tags,
          is_archived: false,
        })
        .select()
        .single();

      if (error) throw error;

      setBookmarks((prev) => [data, ...prev]);
      return data;
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  };

  const updateBookmark = async (id: number, updates: Partial<Bookmark>) => {
    if (!user) throw new Error('User not authenticated');

    try {
      const { data, error } = await supabase
        .from('bookmarks')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) throw error;

      setBookmarks((prev) => prev.map((bookmark) => (bookmark.id === id ? data : bookmark)));
      return data;
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  };

  const deleteBookmark = async (id: number) => {
    if (!user) throw new Error('User not authenticated');

    try {
      const { error } = await supabase
        .from('bookmarks')
        .delete()
        .eq('id', id)
        .eq('user_id', user.id);

      if (error) throw error;

      setBookmarks((prev) => prev.filter((bookmark) => bookmark.id !== id));
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  };

  const archiveBookmark = async (id: number) => {
    return updateBookmark(id, { is_archived: true });
  };

  const unarchiveBookmark = async (id: number) => {
    return updateBookmark(id, { is_archived: false });
  };

  const markAsRead = async (id: number) => {
    return updateBookmark(id, { last_open: new Date().toISOString() });
  };

  return {
    bookmarks,
    isLoading,
    error,
    addBookmark,
    updateBookmark,
    deleteBookmark,
    archiveBookmark,
    unarchiveBookmark,
    markAsRead,
    refetch: fetchBookmarks,
  };
}

export function useBookmarkStats() {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    total: 0,
    unread: 0,
    archived: 0,
    weeklyGrowth: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      if (!user) {
        setStats({ total: 0, unread: 0, archived: 0, weeklyGrowth: 0 });
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // Get total bookmarks
        const { count: total } = await supabase
          .from('bookmarks')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id);

        // Get unread bookmarks (no last_open date)
        const { count: unread } = await supabase
          .from('bookmarks')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id)
          .is('last_open', null);

        // Get archived bookmarks
        const { count: archived } = await supabase
          .from('bookmarks')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id)
          .eq('is_archived', true);

        // Get weekly growth (bookmarks created in the last 7 days)
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);

        const { count: weeklyGrowth } = await supabase
          .from('bookmarks')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id)
          .gte('created_at', weekAgo.toISOString());

        setStats({
          total: total || 0,
          unread: unread || 0,
          archived: archived || 0,
          weeklyGrowth: weeklyGrowth || 0,
        });
      } catch (error) {
        console.error('Error fetching bookmark stats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, [user]);

  return { stats, isLoading };
}

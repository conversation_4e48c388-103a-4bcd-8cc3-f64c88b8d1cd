'use client';

import { BookmarkGrid } from '@/components/app/bookmark-grid';
import { SearchBar } from '@/components/app/search-bar';
import { useLanguage } from '@/hooks/use-language';
import { useSearchParams } from 'next/navigation';
import { useMemo } from 'react';

export default function BookmarksPage() {
  const { t } = useLanguage();
  const searchParams = useSearchParams();

  const searchQuery = searchParams.get('search');

  const filters = useMemo(() => {
    const baseFilters = { is_archived: false };
    if (searchQuery) {
      return { ...baseFilters, search: searchQuery };
    }
    return baseFilters;
  }, [searchQuery]);

  const pageTitle = searchQuery ? `"${searchQuery}" 검색 결과` : t.allBookmarks;

  return (
    <div className="p-6 space-y-6">
      {/* Search Bar */}
      <SearchBar />

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">{pageTitle}</h1>
          {searchQuery && (
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              "{searchQuery}"에 대한 북마크 검색 결과입니다
            </p>
          )}
        </div>
      </div>

      <BookmarkGrid filters={filters} title={pageTitle} showHeader={false} />
    </div>
  );
}

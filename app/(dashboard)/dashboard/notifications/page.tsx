'use client';

import { useState } from 'react';
import { useUserSettings } from '@/hooks/use-user-settings';
import { useLanguage } from '@/hooks/use-language';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { Bell, Check, CheckCheck, Globe, User } from 'lucide-react';
import { format } from 'date-fns';
import { ko, enUS } from 'date-fns/locale';
import type { Notification } from '@/types';

export default function NotificationsPage() {
  const { t, language } = useLanguage();
  const { notifications, markNotificationAsRead } = useUserSettings();
  const [activeTab, setActiveTab] = useState<'all' | 'personal' | 'global'>('all');

  const personalNotifications = notifications.filter((n) => n.type === 'personal');
  const globalNotifications = notifications.filter((n) => n.type === 'global');
  const unreadCount = notifications.filter((n) => !n.is_read).length;

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markNotificationAsRead(notificationId);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      const unreadNotifications = notifications.filter((n) => !n.is_read);
      await Promise.all(
        unreadNotifications.map((notification) => markNotificationAsRead(notification.id)),
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  };

  const renderNotificationList = (notificationList: Notification[]) => {
    if (notificationList.length === 0) {
      return (
        <div className="text-center py-12">
          <Bell className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            {t.noNotifications || 'No notifications'}
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            {activeTab === 'personal'
              ? '개인 알림이 없습니다.'
              : activeTab === 'global'
                ? '전체 공지가 없습니다.'
                : '알림이 없습니다.'}
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {notificationList.map((notification) => (
          <Card
            key={notification.id}
            className={`transition-colors ${
              !notification.is_read
                ? 'bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800'
                : 'hover:bg-gray-50 dark:hover:bg-gray-800'
            }`}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-2">
                    {notification.type === 'global' ? (
                      <Globe className="h-4 w-4 text-blue-500" />
                    ) : (
                      <User className="h-4 w-4 text-green-500" />
                    )}
                    <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {notification.title}
                    </h3>
                    {!notification.is_read && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
                    )}
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 leading-relaxed">
                    {notification.body}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {format(new Date(notification.created_at), 'PPp', {
                          locale: language === 'ko' ? ko : enUS,
                        })}
                      </span>
                      <Badge
                        variant={notification.type === 'global' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {notification.type === 'global' ? '전체 공지' : '개인 알림'}
                      </Badge>
                    </div>

                    {!notification.is_read && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleMarkAsRead(notification.id)}
                        className="text-xs"
                      >
                        <Check className="h-3 w-3 mr-1" />
                        읽음 처리
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">{t.notifications}</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">알림을 확인하고 관리하세요</p>
        </div>

        {unreadCount > 0 && (
          <Button onClick={handleMarkAllAsRead} variant="outline">
            <CheckCheck className="h-4 w-4 mr-2" />
            모두 읽음 처리 ({unreadCount})
          </Button>
        )}
      </div>

      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as any)}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all" className="flex items-center space-x-2">
            <Bell className="h-4 w-4" />
            <span>전체 ({notifications.length})</span>
          </TabsTrigger>
          <TabsTrigger value="personal" className="flex items-center space-x-2">
            <User className="h-4 w-4" />
            <span>개인 알림 ({personalNotifications.length})</span>
          </TabsTrigger>
          <TabsTrigger value="global" className="flex items-center space-x-2">
            <Globe className="h-4 w-4" />
            <span>전체 공지 ({globalNotifications.length})</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          {renderNotificationList(notifications)}
        </TabsContent>

        <TabsContent value="personal" className="mt-6">
          {renderNotificationList(personalNotifications)}
        </TabsContent>

        <TabsContent value="global" className="mt-6">
          {renderNotificationList(globalNotifications)}
        </TabsContent>
      </Tabs>
    </div>
  );
}

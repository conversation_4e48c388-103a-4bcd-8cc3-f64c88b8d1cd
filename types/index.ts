export interface User {
  id: string;
  name: string;
  avatar: string;
  email?: string;
  plan_id?: string;
  created_at?: string;
  last_login_at?: string;
  deleted_at?: string;
  archived_count?: number;
}

export interface Bookmark {
  id: number;
  url: string;
  title?: string;
  summary?: string;
  tags?: string[];
  is_archived: boolean;
  created_at: string;
  updated_at: string;
  needs_reembed?: boolean;
  last_open?: string | null;
  logo_url?: string | null;
}

export interface Embedding {
  bookmark_id: number;
  embedding: number[];
  embedded_at: string;
}

export interface SearchResult {
  id: number;
  title: string;
  url: string;
  snippet: string;
  relevance: number;
  summary?: string;
  tags?: string[];
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  isLoading: boolean;
}

export interface Session {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  expires_at: number;
  user: User;
}

export interface ClientMeta {
  title?: string;
  description?: string;
  url: string;
  timestamp: number;
}

export interface SearchFilters {
  tags?: string[];
  is_archived?: boolean;
  before?: string;
  after?: string;
  includeArchived?: boolean;
  search?: string;
}

// New types for the updated database schema
export interface Plan {
  id: string;
  name: string;
  price_cents: number;
  billing_period: string;
  description?: string;
  created_at: string;
}

export interface SocialAccount {
  id: string;
  user_id: string;
  provider: string;
  provider_uid: string;
  email?: string;
  created_at: string;
}

export interface Notification {
  id: string;
  user_id?: string;
  type: 'global' | 'personal';
  title: string;
  body: string;
  is_read: boolean;
  created_at: string;
}

export interface Question {
  id: string;
  user_id: string;
  content: string;
  created_at: string;
  is_archived: boolean;
  archived_at?: string;
  expires_at: string;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: 'active' | 'canceled' | 'trial' | 'past_due';
  started_at: string;
  ends_at?: string;
  canceled_at?: string;
}

export interface UserSettings {
  id: string;
  user_id: string;
  personal_notifications: boolean;
  global_notifications: boolean;
  weekly_summary_email: boolean;
  ai_style: 'summary' | 'friendly' | 'technical';
  created_at: string;
  updated_at: string;
}
